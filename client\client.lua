local stripModel = `xs_prop_arena_spikes_02a`
local treeModel = `baller5`
local treeCoords = vec4(1695.43, 3248.37, 40.19, 284.95)
local treeEntity

local range = 0.3
local intensity = 300.0

local bones = {
	{ bone = "wheel_lf", index = 0 },
	{ bone = "wheel_rf", index = 1 },
}

local countingDown = false
local states = {}

local strips = {
    ["left"] = {
        coords = vec4(1709.60, 3245.85, 39.22, 285.3834),
        entity = nil
    },
    ["right"] = {
        coords = vec4(1706.49, 3257.64, 39.21, 285.3828),
        entity = nil
    }
}

local objects = {}
local dragThreadId
local lightThreadId

local function enteredDrag()
    for k, v in pairs(strips) do
        lib.requestModel(stripModel)
        local object = CreateObject(stripModel, v.coords.x, v.coords.y, v.coords.z, false, false, false)
        SetEntityHeading(object, v.coords.w)
        FreezeEntityPosition(object, true)
        SetEntityCollision(object, false, false)
        -- SetEntityAlpha(object, 0, false)
        PlaceObjectOnGroundProperly(object)

        strips[k].entity = object
    end

    lib.requestModel(treeModel)
    treeEntity = CreateVehicle(treeModel, treeCoords.x, treeCoords.y, treeCoords.z, treeCoords.w, false, false)
    FreezeEntityPosition(treeEntity, true)
    SetEntityCollision(treeEntity, false, false)
    SetEntityHeading(treeEntity, treeCoords.w)
    SetVehicleDoorsLocked(treeEntity, 10)
    SetVehicleDoorsLockedForAllPlayers(treeEntity, true)
    SetEntityInvincible(treeEntity, true)

    if dragThreadId then TerminateThread(dragThreadId) dragThreadId = nil end
    dragThreadId = CreateThread(function()
        local cacheStaged = {
            left = false,
            right = false,
            side = false
        }

        local wheelsStaged = {
            left = false,
            right = false,
            side = false
        }
        while dragThreadId do
            if not cache.vehicle then
                wheelsStaged = {
                    left = false,
                    right = false,
                    side = false
                }
                cacheStaged = {
                    left = false,
                    right = false,
                    side = false
                }

                Wait(1000)
            else
                Wait(500)

                for _, v in pairs(bones) do
                    local boneIndex = GetEntityBoneIndexByName(cache.vehicle, v.bone)
                    local boneCoords = GetWorldPositionOfEntityBone(cache.vehicle, boneIndex)
                    local wheelTouchingLeft =  IsPointInAngledArea(
                        boneCoords.x, boneCoords.y, boneCoords.z,
                        strips.left.coords.x, strips.left.coords.y - 2.54, strips.left.coords.z,
                        strips.left.coords.x, strips.left.coords.y + 2.54, strips.left.coords.z,
                        0.9, false, false
                    )

                    local wheelTouchingRight =  IsPointInAngledArea(
                        boneCoords.x, boneCoords.y, boneCoords.z,
                        strips.right.coords.x, strips.right.coords.y - 2.54, strips.right.coords.z,
                        strips.right.coords.x, strips.right.coords.y + 2.54, strips.right.coords.z,
                        0.9, false, false
                    )

                    if wheelTouchingLeft then
                        if v.index == 0 then
                            wheelsStaged.left = true
                        else
                            wheelsStaged.right = true
                        end

                        wheelsStaged.side = "left"
                    elseif wheelTouchingRight then
                        if v.index == 0 then
                            wheelsStaged.left = true
                        else
                            wheelsStaged.right = true
                        end

                        wheelsStaged.side = "right"
                    else
                        if v.index == 0 then
                            wheelsStaged.left = false
                        else
                            wheelsStaged.right = false
                        end
                    end
                end

                if cacheStaged.left ~= wheelsStaged.left or cacheStaged.right ~= wheelsStaged.right then
                    cacheStaged = lib.table.clone(wheelsStaged)

                    if cacheStaged.left and cacheStaged.right then
                        TriggerServerEvent("filo_drag:server:setStaged", cacheStaged.side, true)
                        TriggerServerEvent("filo_drag:server:earlyStart", cacheStaged.side, false)
                    else
                        if states[cacheStaged.side].staged then
                            TriggerServerEvent("filo_drag:server:setStaged", cacheStaged.side, false)
                        end
                    end
                end

                if countingDown and states[cacheStaged.side].light ~= 4 then
                    if not cacheStaged.left or not cacheStaged.right then
                        TriggerServerEvent("filo_drag:server:earlyStart", cacheStaged.side, true)
                    end
                end
            end
        end
    end)

    if lightThreadId then TerminateThread(lightThreadId) lightThreadId = nil end
    lightThreadId = CreateThread(function()
        while lightThreadId do
            Wait(10)

            local _, forwardVector, _, position = GetEntityMatrix(treeEntity)
            local x, y, z, w = GetEntityQuaternion(treeEntity)
            local fw = forwardVector
            local angle = 90
            x = math.cos(angle) * fw.x - math.sin(angle) * fw.y
            y = math.sin(angle) * fw.x + math.cos(angle) * fw.y
            fw = vector3(x, y, fw.z)

            local i_lr = GetWorldPositionOfEntityBone(treeEntity, GetEntityBoneIndexByName(treeEntity, 'indicator_lf')) -- 1st left
            local i_rr = GetWorldPositionOfEntityBone(treeEntity, GetEntityBoneIndexByName(treeEntity, 'indicator_rf')) -- 1st right

            lightState((i_lr + fw * 0.2), "left")
            lightState((i_rr + fw * 0.2), "right")

            if states.left.staged and states.right.staged then
                SetVehicleEngineOn(treeEntity, true, true, true)
            else
                if not states.left.early and not states.right.early then
                    SetVehicleEngineOn(treeEntity, false, true, true)
                end
            end
        end
    end)
end

local function exitDrag()
    for k, v in pairs(strips) do
        SetEntityAsMissionEntity(v.entity, false, true)
        DeleteEntity(v.entity)
    end

    SetEntityAsMissionEntity(treeEntity, false, true)
    DeleteEntity(treeEntity)

    if dragThreadId then TerminateThread(dragThreadId) dragThreadId = nil end
    if lightThreadId then TerminateThread(lightThreadId) lightThreadId = nil end

end

function lightState(coords, side)
    if states[side].light == 1 and not states[side].early then
        DrawLightWithRange(coords + vector3(0, 0, 2), 255, 255, 0, range, intensity)
    elseif states[side].light == 2 and not states[side].early then
        DrawLightWithRange(coords + vector3(0, 0, 2), 255, 255, 0, range, intensity)
        DrawLightWithRange(coords + vector3(0, 0, 1.5), 255, 255, 0, range, intensity) -- 2nd left
    elseif states[side].light == 3 and not states[side].early then
        DrawLightWithRange(coords + vector3(0, 0, 2), 255, 255, 0, range, intensity)
        DrawLightWithRange(coords + vector3(0, 0, 1.5), 255, 255, 0, range, intensity) -- 2nd left
        DrawLightWithRange(coords + vector3(0, 0, 1), 255, 255, 0, range, intensity) -- 3rd left
    elseif states[side].light == 4 and not states[side].early then
        DrawLightWithRange(coords + vector3(0, 0, 2), 255, 255, 0, range, intensity)
        DrawLightWithRange(coords + vector3(0, 0, 1.5), 255, 255, 0, range, intensity) -- 2nd left
        DrawLightWithRange(coords + vector3(0, 0, 1), 255, 255, 0, range, intensity) -- 3rd left
        DrawLightWithRange(coords + vector3(0, 0, 0.5), 0, 255, 0, range, intensity) -- 4th left
    end

    if states[side].early then
        SetVehicleEngineOn(treeEntity, true, true, true)
        SetVehicleIndicatorLights(treeEntity, side == "left" and 1 or 0, true)
    else
        SetVehicleIndicatorLights(treeEntity, side == "left" and 1 or 0, false)
    end
end

local function playerLoaded()
    states = lib.callback.await("filo_drag:server:fetchStates", 1000)

    lib.zones.box({
        coords = vec3(1392.34, 3168.78, 41.18),
        size = vec3(250.0, 1000.0, 100.0),
        rotation = 281.58,
        debug = false,
        onEnter = function()
            enteredDrag()
        end,
        onExit = function()
            exitDrag()
        end
    })
end

local function playerUnloaded()
    for _, v in pairs(objects) do
        SetEntityAsMissionEntity(v, false, true)
        DeleteEntity(v)
    end

    if treeEntity then
        SetEntityAsMissionEntity(treeEntity, false, true)
        DeleteEntity(treeEntity)
    end

    if dragThreadId then TerminateThread(dragThreadId) end
end

RegisterNetEvent("filo_drag:client:setStaged", function(side, staged)
    states[side].staged = staged
end)

RegisterNetEvent("filo_drag:client:setLight", function(side, light)
    states[side].light = light
end)

RegisterNetEvent("filo_drag:client:setCounting", function(counting)
    countingDown = counting
end)

RegisterNetEvent("filo_drag:client:setEarly", function(side, state)
    states[side].early = state
end)

RegisterNetEvent('QBCore:Client:OnPlayerLoaded', playerLoaded)

AddEventHandler('onResourceStop', function(name)
    if name ~= cache.resource then return end
    playerUnloaded()
end)

AddEventHandler('onResourceStart', function(name)
    if name ~= cache.resource then return end
    if not LocalPlayer.state.isLoggedIn then return end
    playerLoaded()
end)